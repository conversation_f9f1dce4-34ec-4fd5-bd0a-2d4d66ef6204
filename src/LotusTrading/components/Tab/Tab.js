import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import { isEmpty } from 'lodash';
import { SOURCE } from '../../config/lotusTradingConfig';

import styles from './Tab.scss';

const Tab = ({
  children,
  customHeader,
  tabs,
  source,
  tabContainerCustomClass,
  activeTab,
  tabOnClick,
  customActiveTab,
  showUnderline = true,
}) => {
  const tabClickHandler = tab => {
    if (tab === activeTab) return;
    tabOnClick(tab);
  };

  const activeTabClass = tab => tab.toLowerCase() === activeTab.toLowerCase();

  return (
    <>
      <div
        className={cx(styles.tabContainer, tabContainerCustomClass, {
          [styles.scrollableTabs]: source === SOURCE.PERSONALIZED_TRADING_IDEAS,
        })}
      >
        {!isEmpty(customHeader) ? (
          customHeader
        ) : (
          <>
            {tabs.map(tab => (
              <div
                className={cx(styles.tab, {
                  [styles.activeTabClass]: activeTabClass(tab),
                  [customActiveTab]: customActiveTab && activeTabClass(tab),
                })}
                key={tab}
                onClick={() => tabClickHandler(tab)}
              >
                <span>
                  {source === SOURCE.PERSONALIZED_TRADING_IDEAS
                    ? `From ${tab.charAt(0).toUpperCase() + tab.slice(1)}`
                    : tab.charAt(0).toUpperCase() + tab.slice(1)}
                </span>
                {showUnderline
                  ? activeTabClass(tab) && <div className={styles.underline} />
                  : null}
              </div>
            ))}
          </>
        )}
      </div>
      {children}
    </>
  );
};

Tab.defaultProps = {
  customHeader: null,
  tabContainerCustomClass: '',
  tabs: null,
};

Tab.propTypes = {
  customHeader: PropTypes.element,
  tabContainerCustomClass: PropTypes.string,
  tabs: PropTypes.arrayOf(PropTypes.string),
  activeTab: PropTypes.string.isRequired,
  tabOnClick: PropTypes.func.isRequired,
};

export default Tab;
