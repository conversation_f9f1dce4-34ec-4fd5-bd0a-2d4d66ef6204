.tabContainer {
  z-index: 3;
  display: flex;
  padding: 0 10px;
  align-items: center;
  border-bottom: solid 1px map-get($colors, Gray1);

  .tab {
    text-align: center;
    white-space: nowrap;

    cursor: pointer;

    gap: 10px;
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;

    padding: 12px 10px 10px 10px;

    @include typography(heading2B3, map-get($colors, GreySecondary), false, true);
    font-weight: 400;

    &.activeTabClass {
      @include typography(heading2B3, map-get($colors, PrimaryColor), false, true);
      font-weight: 600;

      padding-bottom: 0;
    }
  }

  .underline {
    width: 24px;
    height: 1.4px;
    border-radius: 20px;
    background-color: map-get($colors, BrandPrimary);
  }
}

.scrollableTabs {
  overflow-x: auto;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}
