import { BASE_URL } from '../../config/envConfig';
import { env, EQ_HOST } from '../../config/urlConfig';
import { isPaytmMoney } from '../../utils/commonUtils';

const { KYC_HOST, ORDERS_API_HOST } = BASE_URL;

export const TNC_URL = {
  GET_TNC_STATUS: userId =>
    `${KYC_HOST}userprofile/v1/user/${userId}/tnc-accept/verify`,
  POST_TNC_STATUS: userId =>
    `${KYC_HOST}userprofile/v1/user/${userId}/tnc-accept`,
};

export const PML_ORDER_URL_DERIVATIVES = 'https://paytmmoney.com/stocks';
export const PML_ORDER_URL_EQUITY = 'https://paytmmoney.com/stocks/company';
export const PAYTM_ORDER_URL = isBuy =>
  isBuy
    ? 'paytmmp://paytmmoney/stocks/place-order-buy'
    : 'paytmmp://paytmmoney/stocks/place-order-sell';
export const KYC_DEEP_LINK = isPaytmMoney()
  ? 'https://www.paytmmoney.com/stocks/kyc/ir'
  : 'paytmmp://paytmmoney/stocks/ipo-kyc';
export const MUHURAT_TRADING_ONELINK =
  'https://paytmmoney.onelink.me/9L59/MuharatTradingLink';
export const PAYTM_DEEPLINK = 'paytmmp://paytmmoney/stocks/company';

export const GENERIC_API_URL = {
  READINESS: userId =>
    `${KYC_HOST}userprofile/user/${userId}/v3/readiness?product=EQUITY`,
  MARGIN_CALCULATOR: `${EQ_HOST}margin/calculator/api/v1/scrips`,
};

export const TRADING_IDEAS_URL = `${EQ_HOST}advisor-api/jamoon/getResult`;
export const JAMOON_FALLBACK_URL =
  env === 'staging'
    ? 'https://static.paytmmoney.com/data/staging/v1/jamoon_fallback.json'
    : 'https://static.paytmmoney.com//data/production/v1/jamoon_fallback.json';

export const CLEVERTAP_STOREFRONT = () =>
  env === 'staging'
    ? 'https://storefront.paytmmoney.com/v2/h/prod-pml-advisorymasterlandingpage'
    : 'https://storefront.paytmmoney.com/v2/h/prod-pml-advisorymasterlandingpage';
export const CLEAVERTAP_ACCOUNT_ID = 'WWK-K4Z-995Z';
export const CLEAVERTAP_PASSCODE = 'c81122c5eb9f4c928ac50a906c391aac';

export const USER_SENTIMENT = pml_id =>
  `${EQ_HOST}data-eq-user-profile/mkt-sentiment/api/v1/user/sentiment?pml_id=${pml_id}`;
export const ALL_USER_SENTIMENT = `${EQ_HOST}data-eq-user-profile/mkt-sentiment/api/v1/sentiment`;
export const POST_USER_SENTIMENT = `${EQ_HOST}data-eq-user-profile/mkt-sentiment/api/v1/user/sentiment`;
export const P_CLOSE_URL = 'https://api-eq-stg.paytmmoney.com/data/v2/pclose';

export const HOLDINGS_API = () =>
  `${EQ_HOST}holdings/v1/get-user-holdings-data`;

export const POSITIONS_API = `${ORDERS_API_HOST}order/info/v1/position`;

export const BADGE_SERVICE_API = `${EQ_HOST}pml-core-badge-service/v1/list`;

export const SEARCHED_API = `${EQ_HOST}data/v3/recent/searched`;

export const WATCHLIST_SEC_API = value =>
  `${EQ_HOST}marketwatch/api/v1/watchlist/${value}/security`;

export const WATCHLIST_API = `${EQ_HOST}marketwatch/api/v1/watchlist`;
