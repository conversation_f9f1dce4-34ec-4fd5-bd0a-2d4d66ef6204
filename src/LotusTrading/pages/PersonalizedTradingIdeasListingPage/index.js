import React, { useEffect } from 'react';

import { usePersonalizedTradingIdeasContext } from '../../context/PersonalizedTradingIdeasContext';
import baseComponent from '../../../HOC/BaseComponent/BaseComponent';

import { exitApp } from '../../../utils/bridgeUtils';

import { TRADING_IDEAS } from '../../constants';

import {
  PERSONALIZED_TRADING_IDEAS_TABS,
  QUICK_FILTERS_DERIVATIVES,
  SAVED_FILTERS_KEY,
  SOURCE,
} from '../../config/lotusTradingConfig';

import {
  sendLandingPageOpenEvent,
  sendQuickFilterClickEvent,
  sendTabChangeEvent,
} from '../../analyticsEvents/tradingIdeasEventUtils';

import {
  EVENT_CATEGORY,
  USER_ACTION,
} from '../../analyticsEvents/tradingIdeasEventEnum';

// eslint-disable-next-line import/extensions,import/no-unresolved
import TradingIdeasListWrapperPage from './__BUILD_PATH__';

function PersonalizedTradingIdeasListingPage(props) {
  const {
    activeTab,
    activeQuickFilter,
    setActiveTab,
    fetchTradingIdeas,
    setActiveQuickFilter,
    tiBadgeData,
  } = usePersonalizedTradingIdeasContext(props);

  const handleTabChange = tab => {
    sendTabChangeEvent(EVENT_CATEGORY.TRADECALLS_HOME, tab);
    sendQuickFilterClickEvent(
      EVENT_CATEGORY.TRADECALLS_HOME,
      tab,
      activeQuickFilter,
    );
    setActiveTab(tab);
  };

  const getTradingIdeas = () => {
    setActiveQuickFilter(QUICK_FILTERS_DERIVATIVES[0].param);
    const params = `?clientId=*********&reportname=standard_open`;
    fetchTradingIdeas(params);
  };

  useEffect(() => {
    // PULSE EVENTS
    sendTabChangeEvent(EVENT_CATEGORY.TRADECALLS_HOME, activeTab);
    sendQuickFilterClickEvent(
      EVENT_CATEGORY.TRADECALLS_HOME,
      activeTab,
      activeQuickFilter,
    );
  }, [QUICK_FILTERS_DERIVATIVES[0].param]);

  useEffect(() => {
    sendLandingPageOpenEvent(
      EVENT_CATEGORY.TRADECALLS_HOME,
      USER_ACTION.RAHOME_OPEN,
    );
    const filters = localStorage.getItem(SAVED_FILTERS_KEY);
    if (filters) {
      localStorage.removeItem(SAVED_FILTERS_KEY);
    }
    getTradingIdeas();
  }, []);

  const handleBackPress = () => {
    exitApp();
  };

  return (
    <TradingIdeasListWrapperPage
      isRefreshIcon
      getTradingIdeas={getTradingIdeas}
      isBackIcon
      onBackIconClick={handleBackPress}
      isHamburgerIcon
      title={TRADING_IDEAS}
      tabs={PERSONALIZED_TRADING_IDEAS_TABS}
      activeTab={activeTab}
      handleTabChange={handleTabChange}
      parentProps={props}
      tiBadgeData={tiBadgeData}
      source={SOURCE.PERSONALIZED_TRADING_IDEAS}
    />
  );
}

export default baseComponent(PersonalizedTradingIdeasListingPage);
