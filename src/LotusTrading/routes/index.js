import {
  LOTUS_TRADING_ROUTES,
  FESTIVE_TRADING_ROUTES,
  MUHURAT_TRADING_ROUTES,
} from '../config/routeConfig';

const lotusTradingCommonRoute = [
  {
    path: LOTUS_TRADING_ROUTES.LISTING,
    load: () =>
      import(
        /* webpackChunkName: 'LotusTradingListing' */ './LotusTradingListing'
      ),
  },
  {
    path: LOTUS_TRADING_ROUTES.TNC,
    load: () =>
      import(/* webpackChunkName: 'LotusTradingTnc' */ './LotusTradingTnc'),
  },
  {
    path: LOTUS_TRADING_ROUTES.EXPERTS,
    load: () =>
      import(/* webpackChunkName: 'ExpertsLanding' */ './ExpertsLandingPage'),
  },
];

const lotusTradingMobileRoute = [
  {
    path: LOTUS_TRADING_ROUTES.MASTER_LANDING_TNC,
    load: () =>
      import(/* webpackChunkName: 'LotusTradingTnc' */ './MasterLandingTnc'),
  },
  {
    path: LOTUS_TRADING_ROUTES.BANNER_VIDEO,
    load: () =>
      import(/* webpackChunkName: 'AdvisaryVideo' */ './AdvisaryVideoPage'),
  },
  {
    path: LOTUS_TRADING_ROUTES.ADVISARY_LANDING_PAGE,
    load: () =>
      import(/* webpackChunkName: 'AdvisaryLanding' */ './AdvisaryLandingPage'),
  },
];

const lotusTradingRoute = [
  ...lotusTradingCommonRoute,
  ...lotusTradingMobileRoute,
];

const festiveTradingRoute = [
  {
    path: FESTIVE_TRADING_ROUTES.LISTING,
    load: () =>
      import(
        /* webpackChunkName: 'FestiveTradingListing' */ './FestiveTradingListing'
      ),
  },
  {
    path: FESTIVE_TRADING_ROUTES.TNC,
    load: () =>
      import(/* webpackChunkName: 'FestiveTradingTnc' */ './FestiveTradingTnc'),
  },
];

const muhuratTradingRoute = [
  {
    path: MUHURAT_TRADING_ROUTES.LISTING,
    load: () =>
      import(
        /* webpackChunkName: 'MuhuratTradingListing' */ './MuhuratTradingListing'
      ),
  },
  {
    path: MUHURAT_TRADING_ROUTES.TNC,
    load: () =>
      import(/* webpackChunkName: 'MuhuratTradingTnc' */ './MuhuratTradingTnc'),
  },
];

export {
  lotusTradingRoute,
  lotusTradingCommonRoute,
  festiveTradingRoute,
  muhuratTradingRoute,
};
